import {
  getDataSyncConfigInfoUsingGET,
  getKafkaContentsUsingGET,
  getTopicPartitionsUsingGET,
} from '@/services/dsp/dataSyncConfigInfoController';
import { getDataSyncConfigTableChangeInfoByFilterUsingGET } from '@/services/dsp/dataSyncConfigTableChangeController';
import { SearchOutlined } from '@ant-design/icons';
import { PageContainer, ProCard, ProTable } from '@ant-design/pro-components';
import { request, useLocation, useParams, useRequest } from '@umijs/max';
import {
  Button,
  Col,
  Descriptions,
  Divider,
  Input,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tree,
} from 'antd';
import type { DataNode } from 'antd/es/tree';
import React, { useState } from 'react';

const SyncDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const tableNames = searchParams.get('tableNames')?.split(',') || [];
  const topicName = searchParams.get('topicName');

  // State for modal visibility
  const [isModalVisible, setIsModalVisible] = useState(false);
  // State for change records modal visibility
  const [isChangeRecordsModalVisible, setIsChangeRecordsModalVisible] = useState(false);
  // State for change records pagination
  const [changeRecordsPagination, setChangeRecordsPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  // State for change records search filters
  const [changeRecordsFilters, setChangeRecordsFilters] = useState({
    tableName: '',
    columnOld: '',
    columnNow: '',
  });
  // State for tree data
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  // State for expanded keys
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  // State for Kafka table data
  const [currentOffset, setCurrentOffset] = useState(0);
  const [offsetInput, setOffsetInput] = useState(0);
  const [selectedPartition, setSelectedPartition] = useState<number | undefined>(undefined);
  const [minOffset, setMinOffset] = useState(0); // 新增最小offset
  const [previewContent, setPreviewContent] = useState<string>(''); // 新增预览内容 state

  const handleDownloadExcel = () => {
    request('/api/dataSyncConfigInfo/exportLog', {
      method: 'GET',
      // data: record,
      responseType: 'blob',
      getResponse: true,
      skipErrorHandler: true,
    })
      .then((response) => {
        // 生成当前时间戳
        const timestamp = Date.now();
        const fileName = `${timestamp}_log.zip`;

        const url = URL.createObjectURL(new Blob([response.data], { type: 'application/zip' }));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', fileName); // 使用时间戳_log.zip格式的文件名
        document.body.appendChild(link);
        link.click();

        // 清理URL对象
        URL.revokeObjectURL(url);
        document.body.removeChild(link);
      })
      .catch(() => {
        message.error('下载失败');
      });
  };

  const { data } = useRequest(
    () => getDataSyncConfigInfoUsingGET({ dataSyncConfigId: id as unknown as number }),
    {
      ready: !!id,
      formatResult: (res) => {
        return (
          Object.entries(res?.data || {})?.map(([key, value]) => {
            let displayValue = value;
            if (
              key !== 'changeRecords' &&
              typeof value === 'string' &&
              value.startsWith('[') &&
              value.endsWith(']')
            ) {
              try {
                displayValue = JSON.parse(value);
              } catch (e) {
                displayValue = value;
              }
            }
            return {
              key: key,
              label: key,
              children:
                typeof displayValue === 'object'
                  ? JSON.stringify(displayValue, null, 2)
                  : displayValue?.toString() || '',
            };
          }) || []
        );
      },
    },
  );

  // Fetch topic partitions
  const { loading: topicPartitionsLoading, run: fetchTopicPartitions } = useRequest(
    (topic: string) =>
      getTopicPartitionsUsingGET({
        dataSyncConfigInfoId: id as unknown as number,
        topic,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res?.length) {
          const partitions = Array.isArray(res) ? res : [];
          const treeNodes: DataNode[] = [
            {
              title: topicName,
              key: 'topic-' + topicName,
              children: partitions.map((partition: number) => ({
                title: `分区 ${partition}`,
                key: `partition-${partition}`,
                partition: partition,
              })),
            },
          ];
          setTreeData(treeNodes);

          // 设置展开的节点
          const topicKey = 'topic-' + topicName;
          setExpandedKeys([topicKey]);
        }
      },
    },
  );

  // Fetch Kafka contents
  const {
    data: tableData = [],
    loading: kafkaContentsLoading,
    run: fetchKafkaContents,
  } = useRequest(
    (params: { topic: string; partition: number; offset?: number }) =>
      getKafkaContentsUsingGET({
        dataSyncConfigInfoId: id as unknown as number,
        topic: params.topic,
        partition: params.partition,
        offset: params.offset || 0,
      }),
    {
      manual: true,
    },
  );

  // Fetch change records data
  const {
    data: changeRecordsResponse,
    loading: changeRecordsLoading,
    run: fetchChangeRecords,
  } = useRequest(
    (params: { pageNum: number; pageSize: number; filter?: Record<string, any> }) =>
      getDataSyncConfigTableChangeInfoByFilterUsingGET({
        dataSyncConfigId: id as unknown as number,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        filter: params.filter,
      }),
    {
      manual: true,
      onSuccess: (response) => {
        console.log('changeRecordsResponse', response);
        if (response?.code === 100 && response?.data) {
          setChangeRecordsPagination({
            current: response.data.current || changeRecordsPagination.current,
            pageSize: response.data.size || changeRecordsPagination.pageSize,
            total: response.data.total || 0,
          });
        } else {
          setChangeRecordsPagination({
            current: 1,
            pageSize: 10,
            total: 0,
          });
        }
      },
      formatResult: (res) => res,
    },
  );

  // Get change records data from response
  const changeRecordsData = changeRecordsResponse?.data?.records || [];

  // Handle tree node selection
  const handleTreeSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0 && info.node.partition !== undefined) {
      setSelectedPartition(info.node.partition);
      // offset传-1
      fetchKafkaContents({
        topic: topicName || '',
        partition: info.node.partition,
        offset: -1,
      }).then((res: any) => {
        if (Array.isArray(res) && res.length > 0) {
          const firstOffset = res[0].offset;
          setCurrentOffset(firstOffset);
          setOffsetInput(firstOffset);
          setMinOffset(firstOffset);
        } else {
          setCurrentOffset(0);
          setOffsetInput(0);
          setMinOffset(0);
        }
      });
    }
  };

  // Handle offset decrement
  const handleLeftClick = () => {
    if (currentOffset === minOffset) return; // 如果已经是最小offset，直接return，防止多余操作
    const newOffset =
      Number(currentOffset) - 100 < minOffset ? minOffset : Number(currentOffset) - 100;
    setCurrentOffset(newOffset);
    setOffsetInput(newOffset);
    if (selectedPartition !== undefined) {
      fetchKafkaContents({
        topic: topicName || '',
        partition: selectedPartition,
        offset: newOffset,
      });
    }
  };

  // Handle offset increment
  const handleRightClick = () => {
    const newOffset = Number(currentOffset) + 100;
    setCurrentOffset(newOffset);
    setOffsetInput(newOffset);
    if (selectedPartition !== undefined) {
      fetchKafkaContents({
        topic: topicName || '',
        partition: selectedPartition,
        offset: newOffset,
      });
    }
  };

  // 处理树节点展开/折叠
  const handleExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
  };

  // Show modal and fetch topic partitions
  const showModal = () => {
    setIsModalVisible(true);
    if (topicName) {
      fetchTopicPartitions(topicName);
    }
  };

  // Build filter object from search conditions
  const buildFilter = () => {
    const filter: Record<string, any> = {};
    if (changeRecordsFilters.tableName.trim()) {
      filter.tableName = changeRecordsFilters.tableName.trim();
    }
    if (changeRecordsFilters.columnOld.trim()) {
      filter.columnOld = changeRecordsFilters.columnOld.trim();
    }
    if (changeRecordsFilters.columnNow.trim()) {
      filter.columnNow = changeRecordsFilters.columnNow.trim();
    }
    return Object.keys(filter).length > 0 ? filter : undefined;
  };

  // Handle search
  const handleSearch = () => {
    const filter = buildFilter();
    fetchChangeRecords({ pageNum: 1, pageSize: changeRecordsPagination.pageSize, filter });
  };

  // Handle reset search
  const handleResetSearch = () => {
    setChangeRecordsFilters({
      tableName: '',
      columnOld: '',
      columnNow: '',
    });
    fetchChangeRecords({ pageNum: 1, pageSize: changeRecordsPagination.pageSize });
  };

  // Show change records modal
  const showChangeRecordsModal = () => {
    setIsChangeRecordsModalVisible(true);
    fetchChangeRecords({ pageNum: 1, pageSize: 10 });
  };

  // Handle pagination change
  const handleChangeRecordsPaginationChange = (page: number, pageSize?: number) => {
    const filter = buildFilter();
    fetchChangeRecords({ pageNum: page, pageSize: pageSize || 10, filter });
  };

  const labelMap: Record<string, string> = {
    dbName: '表名',
    dbType: '数据库类型',
    dbHost: '数据库IP',
    startTime: '开始时间',
    syncedRows: '已同步数据',
    curTime: '本次运行时长（秒）',
    batchRows: '本次同步数据',
    curOffsets: '当前offsets',
    rps: '效率（条/秒）',
    status: '运行状态',
    exception: '错误信息',
  };

  // 保证 currentOffset 变化时 offsetInput 跟随同步
  React.useEffect(() => {
    setOffsetInput(currentOffset);
  }, [currentOffset]);

  // 保证 minOffset 变化时 currentOffset 也同步（只在 minOffset 变小时同步）
  React.useEffect(() => {
    if (currentOffset < minOffset) {
      setCurrentOffset(minOffset);
      setOffsetInput(minOffset);
    }
  }, [minOffset]);

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProCard
        gutter={24}
        title={
          <div
            style={{
              fontSize: 20,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <span>{`TopicName：${topicName}`}</span>
            <Button style={{ marginLeft: 20 }} type="primary" onClick={showModal}>
              查看
            </Button>
          </div>
        }
        wrap
      >
        <Table<any>
          columns={[
            {
              title: '序号',
              render: (_, __, index) => `${index + 1}`,
              width: 100,
            },
            {
              title: '表名',
              dataIndex: 'tableName',
            },
          ]}
          title={() => (
            <div
              style={{
                fontSize: 16,
                marginLeft: -8,
                fontWeight: 'bold',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div>同步列表</div>
              <Button onClick={showChangeRecordsModal} type="primary">
                查看变更
              </Button>
            </div>
          )}
          size="middle"
          dataSource={tableNames.map((tableName) => ({ tableName }))}
        />
        <Divider style={{ marginBottom: 24 }} />
        <Descriptions
          size="middle"
          title="目标数据库"
          bordered
          style={{ marginBottom: 24 }}
          layout="vertical"
        >
          {data
            ?.filter((item) => ['dbName', 'dbType', 'dbHost'].includes(item.key))
            .map((item) => (
              <Descriptions.Item key={item.key} label={labelMap[item.key]}>
                {item.children}
              </Descriptions.Item>
            ))}
        </Descriptions>
        <Descriptions size="middle" title="同步信息" bordered layout="vertical">
          {data
            ?.filter((item) =>
              ['startTime', 'syncedRows', 'curTime', 'batchRows', 'rps', 'status'].includes(
                item.key,
              ),
            )
            .map((item) => (
              <Descriptions.Item key={item.key} label={labelMap[item.key]}>
                {item.key === 'status'
                  ? ['运行中', '成功', '失败', '未执行', '中止'][item.children] || '未知状态'
                  : item.children}
              </Descriptions.Item>
            ))}
          {data?.find((item) => item.key === 'curOffsets') && (
            <Descriptions.Item label="当前offsets" span={3}>
              {data.find((item) => item.key === 'curOffsets')?.children}
            </Descriptions.Item>
          )}
          {data?.find((item) => item.key === 'exception') && (
            <Descriptions.Item
              label={
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <div>错误信息</div>
                  <Button type="primary" onClick={handleDownloadExcel}>
                    导出日志
                  </Button>
                </div>
              }
              span={3}
            >
              {data?.find((item) => item.key === 'exception')?.children}
            </Descriptions.Item>
          )}
        </Descriptions>
      </ProCard>

      <Modal
        title="分区详情"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        width="80%"
        footer={null}
        destroyOnClose
        maskClosable={false}
      >
        <div style={{ display: 'flex', height: '80vh' }}>
          <div
            style={{
              width: '30%',
              borderRight: '1px solid #f0f0f0',
              padding: '10px',
              position: 'sticky',
              top: 0,
              overflow: 'auto',
            }}
          >
            <Spin spinning={topicPartitionsLoading}>
              <Tree
                treeData={treeData}
                onSelect={handleTreeSelect}
                expandedKeys={expandedKeys}
                onExpand={handleExpand}
              />
            </Spin>
          </div>
          {/* 修改右侧区域：使用 flex 布局，将操作栏和预览区域固定 */}
          <div style={{ width: '70%', display: 'flex', flexDirection: 'column', height: '80vh' }}>
            {/* 固定的顶部操作栏 */}
            <div
              style={{ position: 'sticky', top: 0, background: '#fff', zIndex: 1, padding: '10px' }}
            >
              <div
                style={{
                  marginBottom: 16,
                  display: 'flex',
                  padding: '0 20',
                  justifyContent: 'space-between',
                }}
              >
                <Button
                  type="primary"
                  disabled={currentOffset - 100 < minOffset}
                  onClick={handleLeftClick}
                >
                  上一页
                </Button>
                <div
                  style={{
                    flex: 1,
                    textAlign: 'center',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <span>起始偏移量：</span>
                  <Input
                    style={{ width: 100, margin: '0 8px' }}
                    value={offsetInput}
                    onChange={(e) => {
                      const val = e.target.value;
                      if (/^\d*$/.test(val)) {
                        setOffsetInput(val === '' ? 0 : Number(val));
                      }
                    }}
                    placeholder="正整数"
                  />
                  <Button
                    type="primary"
                    size="small"
                    icon={<SearchOutlined />}
                    onClick={() => {
                      const newOffset = Number(offsetInput);
                      setCurrentOffset(newOffset);
                      if (selectedPartition !== undefined) {
                        fetchKafkaContents({
                          topic: topicName || '',
                          partition: selectedPartition,
                          offset: newOffset,
                        });
                      }
                    }}
                  />
                </div>
                <Button
                  type="primary"
                  disabled={tableData?.length < 100}
                  onClick={handleRightClick}
                >
                  下一页
                </Button>
              </div>
            </div>

            {/* 中间滚动区域：仅包含表格 */}
            <div style={{ flex: 1, padding: '10px' }}>
              <ProTable<any>
                sticky
                scroll={{ x: '100%', y: 'calc(80vh - 480px)' }} // 根据顶部操作栏和底部预览区域高度调整
                dataSource={tableData as Record<string, any>[]}
                columns={[
                  {
                    title: '键',
                    dataIndex: 'key',
                    width: 200,
                    ellipsis: { showTitle: false },
                    render: (text) => (
                      <span
                        style={{ cursor: 'pointer' }}
                        onClick={() => setPreviewContent(text as string)}
                      >
                        {text}
                      </span>
                    ),
                  },
                  {
                    title: '值',
                    dataIndex: 'value',
                    width: 200,
                    ellipsis: { showTitle: false },
                    render: (text) => (
                      <span
                        style={{ cursor: 'pointer' }}
                        onClick={() => setPreviewContent(text as string)}
                      >
                        {text}
                      </span>
                    ),
                  },
                  {
                    title: '偏移量',
                    dataIndex: 'offset',
                    ellipsis: true,
                  },
                  {
                    title: '分区',
                    dataIndex: 'partition',
                    ellipsis: true,
                  },
                  {
                    title: '时间戳',
                    dataIndex: 'timestamp',
                    ellipsis: true,
                  },
                ]}
                pagination={false}
                search={false}
                loading={kafkaContentsLoading}
                rowKey="offset"
                options={{
                  density: false,
                  setting: false,
                  search: false,
                  reload: false,
                }}
              />
            </div>

            {/* 固定的预览区域 */}
            <div
              style={{
                borderTop: '1px solid #f0f0f0',
                padding: '10px',
                background: '#fff',
                height: '300px',
                overflowY: 'auto',
              }}
            >
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>预览：</div>
              <pre style={{ whiteSpace: 'pre-wrap' }}>{previewContent}</pre>
            </div>
          </div>
        </div>
      </Modal>

      {/* 变更记录Modal */}
      <Modal
        title="变更记录"
        open={isChangeRecordsModalVisible}
        onCancel={() => setIsChangeRecordsModalVisible(false)}
        width="80%"
        footer={null}
        destroyOnClose
        maskClosable={true}
      >
        {/* 搜索栏 */}
        <div
          style={{
            marginBottom: 16,
            padding: '16px',
            backgroundColor: '#fafafa',
            borderRadius: '6px',
          }}
        >
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <div style={{ marginBottom: 8, fontWeight: 500 }}>表名</div>
              <Input
                placeholder="请输入表名"
                value={changeRecordsFilters.tableName}
                onChange={(e) =>
                  setChangeRecordsFilters((prev) => ({ ...prev, tableName: e.target.value }))
                }
                onPressEnter={handleSearch}
              />
            </Col>
            <Col span={8}>
              <div style={{ marginBottom: 8, fontWeight: 500 }}>字段旧名</div>
              <Input
                placeholder="请输入字段旧名"
                value={changeRecordsFilters.columnOld}
                onChange={(e) =>
                  setChangeRecordsFilters((prev) => ({ ...prev, columnOld: e.target.value }))
                }
                onPressEnter={handleSearch}
              />
            </Col>
            <Col span={8}>
              <div style={{ marginBottom: 8, fontWeight: 500 }}>字段新名</div>
              <Input
                placeholder="请输入字段新名"
                value={changeRecordsFilters.columnNow}
                onChange={(e) =>
                  setChangeRecordsFilters((prev) => ({ ...prev, columnNow: e.target.value }))
                }
                onPressEnter={handleSearch}
              />
            </Col>
          </Row>
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleResetSearch}>重置</Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          loading={changeRecordsLoading}
          dataSource={changeRecordsData}
          rowKey={(record, index) =>
            `${record.tableName}-${record.columnOld}-${record.columnNow}-${index}`
          }
          size="middle"
          pagination={{
            current: changeRecordsPagination.current,
            pageSize: changeRecordsPagination.pageSize,
            total: changeRecordsPagination.total,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleChangeRecordsPaginationChange,
            onShowSizeChange: handleChangeRecordsPaginationChange,
          }}
          columns={[
            {
              title: '序号',
              render: (_, __, index) =>
                (changeRecordsPagination.current - 1) * changeRecordsPagination.pageSize +
                index +
                1,
              width: 80,
            },
            {
              title: '表名',
              dataIndex: 'tableName',
              key: 'tableName',
              ellipsis: true,
            },
            {
              title: '字段旧名',
              dataIndex: 'columnOld',
              key: 'columnOld',
              ellipsis: true,
            },
            {
              title: '字段新名',
              dataIndex: 'columnNow',
              key: 'columnNow',
              ellipsis: true,
            },
            {
              title: '更新时间',
              dataIndex: 'createTime',
              key: 'createTime',
            },
          ]}
          scroll={{ y: 600 }}
        />
      </Modal>
    </PageContainer>
  );
};

export default SyncDetails;
